{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://apigateway.googleapis.com/", "batchPath": "batch", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/api-gateway/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "apigateway:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://apigateway.mtls.googleapis.com/", "name": "apigateway", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "apigateway.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayLocation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "apigateway.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ApigatewayListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"apis": {"methods": {"create": {"description": "Creates a new Api in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.create", "parameterOrder": ["parent"], "parameters": {"apiId": {"description": "Required. Identifier to assign to the API. Must be unique within scope of the parent resource.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the API, of the form: `projects/*/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apis", "request": {"$ref": "ApigatewayApi"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Api.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "DELETE", "id": "apigateway.projects.locations.apis.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/global/apis/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Api.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/global/apis/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayApi"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}:getIamPolicy", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Apis in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Order by parameters.", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the API, of the form: `projects/*/locations/global`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/apis", "response": {"$ref": "ApigatewayListApisResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Api.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}", "httpMethod": "PATCH", "id": "apigateway.projects.locations.apis.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the API. Format: projects/{project}/locations/global/apis/{api}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Api resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ApigatewayApi"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}:setIamPolicy", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "ApigatewaySetIamPolicyRequest"}, "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}:testIamPermissions", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "ApigatewayTestIamPermissionsRequest"}, "response": {"$ref": "ApigatewayTestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"configs": {"methods": {"create": {"description": "Creates a new ApiConfig in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.configs.create", "parameterOrder": ["parent"], "parameters": {"apiConfigId": {"description": "Required. Identifier to assign to the API Config. Must be unique within scope of the parent resource.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the API Config, of the form: `projects/*/locations/global/apis/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/configs", "request": {"$ref": "ApigatewayApiConfig"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single ApiConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}", "httpMethod": "DELETE", "id": "apigateway.projects.locations.apis.configs.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/global/apis/*/configs/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single ApiConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.configs.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/global/apis/*/configs/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Specifies which fields of the API Config are returned in the response. Defaults to `BASIC` view.", "enum": ["CONFIG_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["", "Do not include configuration source files.", "Include configuration source files."], "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayApiConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}:getIamPolicy", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.configs.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists ApiConfigs in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs", "httpMethod": "GET", "id": "apigateway.projects.locations.apis.configs.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Order by parameters.", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the API Config, of the form: `projects/*/locations/global/apis/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/configs", "response": {"$ref": "ApigatewayListApiConfigsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single ApiConfig.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}", "httpMethod": "PATCH", "id": "apigateway.projects.locations.apis.configs.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the API Config. Format: projects/{project}/locations/global/apis/{api}/configs/{api_config}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the ApiConfig resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ApigatewayApiConfig"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}:setIamPolicy", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.configs.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "ApigatewaySetIamPolicyRequest"}, "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/apis/{apisId}/configs/{configsId}:testIamPermissions", "httpMethod": "POST", "id": "apigateway.projects.locations.apis.configs.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/apis/[^/]+/configs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "ApigatewayTestIamPermissionsRequest"}, "response": {"$ref": "ApigatewayTestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "gateways": {"methods": {"create": {"description": "Creates a new Gateway in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways", "httpMethod": "POST", "id": "apigateway.projects.locations.gateways.create", "parameterOrder": ["parent"], "parameters": {"gatewayId": {"description": "Required. Identifier to assign to the Gateway. Must be unique within scope of the parent resource.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the Gateway, of the form: `projects/*/locations/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/gateways", "request": {"$ref": "ApigatewayGateway"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Gateway.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "DELETE", "id": "apigateway.projects.locations.gateways.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/*/gateways/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Gateway.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "GET", "id": "apigateway.projects.locations.gateways.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the form: `projects/*/locations/*/gateways/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayGateway"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:getIamPolicy", "httpMethod": "GET", "id": "apigateway.projects.locations.gateways.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Gateways in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways", "httpMethod": "GET", "id": "apigateway.projects.locations.gateways.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filter.", "location": "query", "type": "string"}, "orderBy": {"description": "Order by parameters.", "location": "query", "type": "string"}, "pageSize": {"description": "Page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the Gateway, of the form: `projects/*/locations/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/gateways", "response": {"$ref": "ApigatewayListGatewaysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Gateway.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}", "httpMethod": "PATCH", "id": "apigateway.projects.locations.gateways.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. Resource name of the Gateway. Format: projects/{project}/locations/{location}/gateways/{gateway}", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Field mask is used to specify the fields to be overwritten in the Gateway resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "ApigatewayGateway"}, "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:setIamPolicy", "httpMethod": "POST", "id": "apigateway.projects.locations.gateways.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "ApigatewaySetIamPolicyRequest"}, "response": {"$ref": "ApigatewayPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:testIamPermissions", "httpMethod": "POST", "id": "apigateway.projects.locations.gateways.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/gateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "ApigatewayTestIamPermissionsRequest"}, "response": {"$ref": "ApigatewayTestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "apigateway.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "ApigatewayCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "apigateway.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "apigateway.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "ApigatewayOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "apigateway.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ApigatewayListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250409", "rootUrl": "https://apigateway.googleapis.com/", "schemas": {"ApigatewayApi": {"description": "An API that can be served by one or more Gateways.", "id": "ApigatewayApi", "properties": {"createTime": {"description": "Output only. Created time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Display name.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources", "type": "object"}, "managedService": {"description": "Optional. Immutable. The name of a Google Managed Service ( https://cloud.google.com/service-infrastructure/docs/glossary#managed). If not specified, a new Service will automatically be created in the same project as this API.", "type": "string"}, "name": {"description": "Output only. Resource name of the API. Format: projects/{project}/locations/global/apis/{api}", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of the API.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "FAILED", "DELETING", "UPDATING"], "enumDescriptions": ["API does not have a state yet.", "API is being created.", "API is active.", "API creation failed.", "API is being deleted.", "API is being updated."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Updated time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ApigatewayApiConfig": {"description": "An API Configuration is a combination of settings for both the Managed Service and Gateways serving this API Config.", "id": "ApigatewayApiConfig", "properties": {"createTime": {"description": "Output only. Created time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Display name.", "type": "string"}, "gatewayServiceAccount": {"description": "Immutable. The Google Cloud IAM Service Account that Gateways serving this config should use to authenticate to other services. This may either be the Service Account's email (`{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com`) or its full resource name (`projects/{PROJECT}/accounts/{UNIQUE_ID}`). This is most often used when the service is a GCP resource such as a Cloud Run Service or an IAP-secured service.", "type": "string"}, "grpcServices": {"description": "Optional. gRPC service definition files. If specified, openapi_documents must not be included.", "items": {"$ref": "ApigatewayApiConfigGrpcServiceDefinition"}, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources", "type": "object"}, "managedServiceConfigs": {"description": "Optional. Service Configuration files. At least one must be included when using gRPC service definitions. See https://cloud.google.com/endpoints/docs/grpc/grpc-service-config#service_configuration_overview for the expected file contents. If multiple files are specified, the files are merged with the following rules: * All singular scalar fields are merged using \"last one wins\" semantics in the order of the files uploaded. * Repeated fields are concatenated. * Singular embedded messages are merged using these rules for nested fields.", "items": {"$ref": "ApigatewayApiConfigFile"}, "type": "array"}, "name": {"description": "Output only. Resource name of the API Config. Format: projects/{project}/locations/global/apis/{api}/configs/{api_config}", "readOnly": true, "type": "string"}, "openapiDocuments": {"description": "Optional. OpenAPI specification documents. If specified, grpc_services and managed_service_configs must not be included.", "items": {"$ref": "ApigatewayApiConfigOpenApiDocument"}, "type": "array"}, "serviceConfigId": {"description": "Output only. The ID of the associated Service Config ( https://cloud.google.com/service-infrastructure/docs/glossary#config).", "readOnly": true, "type": "string"}, "state": {"description": "Output only. State of the API Config.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "FAILED", "DELETING", "UPDATING", "ACTIVATING"], "enumDescriptions": ["API Config does not have a state yet.", "API Config is being created and deployed to the API Controller.", "API Config is ready for use by Gateways.", "API Config creation failed.", "API Config is being deleted.", "API Config is being updated.", "API Config settings are being activated in downstream systems. API Configs in this state cannot be used by Gateways."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Updated time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ApigatewayApiConfigFile": {"description": "A lightweight description of a file.", "id": "ApigatewayApiConfigFile", "properties": {"contents": {"description": "The bytes that constitute the file.", "format": "byte", "type": "string"}, "path": {"description": "The file path (full or relative path). This is typically the path of the file when it is uploaded.", "type": "string"}}, "type": "object"}, "ApigatewayApiConfigGrpcServiceDefinition": {"description": "A gRPC service definition.", "id": "ApigatewayApiConfigGrpcServiceDefinition", "properties": {"fileDescriptorSet": {"$ref": "ApigatewayApiConfigFile", "description": "Input only. File descriptor set, generated by protoc. To generate, use protoc with imports and source info included. For an example test.proto file, the following command would put the value in a new file named out.pb. $ protoc --include_imports --include_source_info test.proto -o out.pb"}, "source": {"description": "Optional. Uncompiled proto files associated with the descriptor set, used for display purposes (server-side compilation is not supported). These should match the inputs to 'protoc' command used to generate file_descriptor_set.", "items": {"$ref": "ApigatewayApiConfigFile"}, "type": "array"}}, "type": "object"}, "ApigatewayApiConfigOpenApiDocument": {"description": "An OpenAPI Specification Document describing an API.", "id": "ApigatewayApiConfigOpenApiDocument", "properties": {"document": {"$ref": "ApigatewayApiConfigFile", "description": "The OpenAPI Specification document file."}}, "type": "object"}, "ApigatewayAuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "ApigatewayAuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "ApigatewayAuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "ApigatewayAuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "ApigatewayAuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "ApigatewayBinding": {"description": "Associates `members`, or principals, with a `role`.", "id": "ApigatewayBinding", "properties": {"condition": {"$ref": "ApigatewayExpr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "ApigatewayCancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "ApigatewayCancelOperationRequest", "properties": {}, "type": "object"}, "ApigatewayExpr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "ApigatewayExpr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ApigatewayGateway": {"description": "A Gateway is an API-aware HTTP proxy. It performs API-Method and/or API-Consumer specific actions based on an API Config such as authentication, policy enforcement, and backend selection.", "id": "ApigatewayGateway", "properties": {"apiConfig": {"description": "Required. Resource name of the API Config for this Gateway. Format: projects/{project}/locations/global/apis/{api}/configs/{apiConfig}", "type": "string"}, "createTime": {"description": "Output only. Created time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "defaultHostname": {"description": "Output only. The default API Gateway host name of the form `{gateway_id}-{hash}.{region_code}.gateway.dev`.", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Display name.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources", "type": "object"}, "name": {"description": "Output only. Resource name of the Gateway. Format: projects/{project}/locations/{location}/gateways/{gateway}", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The current state of the Gateway.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "FAILED", "DELETING", "UPDATING"], "enumDescriptions": ["Gateway does not have a state yet.", "Gateway is being created.", "Gateway is running and ready for requests.", "Gateway creation failed.", "Gateway is being deleted.", "Gateway is being updated."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Updated time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ApigatewayListApiConfigsResponse": {"description": "Response message for ApiGatewayService.ListApiConfigs", "id": "ApigatewayListApiConfigsResponse", "properties": {"apiConfigs": {"description": "API Configs.", "items": {"$ref": "ApigatewayApiConfig"}, "type": "array"}, "nextPageToken": {"description": "Next page token.", "type": "string"}, "unreachableLocations": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ApigatewayListApisResponse": {"description": "Response message for ApiGatewayService.ListApis", "id": "ApigatewayListApisResponse", "properties": {"apis": {"description": "APIs.", "items": {"$ref": "ApigatewayApi"}, "type": "array"}, "nextPageToken": {"description": "Next page token.", "type": "string"}, "unreachableLocations": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ApigatewayListGatewaysResponse": {"description": "Response message for ApiGatewayService.ListGateways", "id": "ApigatewayListGatewaysResponse", "properties": {"gateways": {"description": "Gateways.", "items": {"$ref": "ApigatewayGateway"}, "type": "array"}, "nextPageToken": {"description": "Next page token.", "type": "string"}, "unreachableLocations": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ApigatewayListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ApigatewayListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "ApigatewayLocation"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ApigatewayListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ApigatewayListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "ApigatewayOperation"}, "type": "array"}}, "type": "object"}, "ApigatewayLocation": {"description": "A resource that represents a Google Cloud location.", "id": "ApigatewayLocation", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "ApigatewayOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "ApigatewayOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "ApigatewayStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "ApigatewayOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "ApigatewayOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "diagnostics": {"description": "Output only. Diagnostics generated during processing of configuration source files.", "items": {"$ref": "ApigatewayOperationMetadataDiagnostic"}, "readOnly": true, "type": "array"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ApigatewayOperationMetadataDiagnostic": {"description": "Diagnostic information from configuration processing.", "id": "ApigatewayOperationMetadataDiagnostic", "properties": {"location": {"description": "Location of the diagnostic.", "type": "string"}, "message": {"description": "The diagnostic message.", "type": "string"}}, "type": "object"}, "ApigatewayPolicy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "ApigatewayPolicy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "ApigatewayAuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "ApigatewayBinding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "ApigatewaySetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "ApigatewaySetIamPolicyRequest", "properties": {"policy": {"$ref": "ApigatewayPolicy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "ApigatewayStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "ApigatewayStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "ApigatewayTestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "ApigatewayTestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ApigatewayTestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "ApigatewayTestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}}, "servicePath": "", "title": "API Gateway API", "version": "v1", "version_module": true}