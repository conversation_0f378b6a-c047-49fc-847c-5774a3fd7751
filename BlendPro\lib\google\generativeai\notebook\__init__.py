# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Notebook extensions for Generative AI."""


def load_ipython_extension(ipython):
    """Register the Colab Magic extension to support %load_ext."""
    # pylint: disable-next=g-import-not-at-top
    from google.generativeai.notebook import magics

    ipython.register_magics(magics.Magics)

    # Since we're in an interactive environment, make the tables prettier.
    try:
        # pylint: disable-next=g-import-not-at-top
        from google import colab  # type: ignore

        colab.data_table.enable_dataframe_formatter()
    except ImportError:
        pass
