{"basePath": "", "baseUrl": "https://acmedns.googleapis.com/", "batchPath": "batch", "canonicalName": "ACME DNS", "description": "Google Domains ACME DNS API that allows users to complete ACME DNS-01 challenges for a domain.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/domains/acme-dns/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "acmedns:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://acmedns.mtls.googleapis.com/", "name": "acmedns", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"acmeChallengeSets": {"methods": {"get": {"description": "Gets the ACME challenge set for a given domain name. Domain names must be provided in Punycode.", "flatPath": "v1/acmeChallengeSets/{rootDomain}", "httpMethod": "GET", "id": "acmedns.acmeChallengeSets.get", "parameterOrder": ["rootDomain"], "parameters": {"rootDomain": {"description": "Required. SLD + TLD domain name to list challenges. For example, this would be \"google.com\" for any FQDN under \"google.com\". That includes challenges for \"subdomain.google.com\". This MAY be Unicode or Punycode.", "location": "path", "required": true, "type": "string"}}, "path": "v1/acmeChallengeSets/{rootDomain}", "response": {"$ref": "AcmeChallengeSet"}}, "rotateChallenges": {"description": "Rotate the ACME challenges for a given domain name. By default, removes any challenges that are older than 30 days. Domain names must be provided in Punycode.", "flatPath": "v1/acmeChallengeSets/{rootDomain}:rotateChallenges", "httpMethod": "POST", "id": "acmedns.acmeChallengeSets.rotateChallenges", "parameterOrder": ["rootDomain"], "parameters": {"rootDomain": {"description": "Required. SLD + TLD domain name to update records for. For example, this would be \"google.com\" for any FQDN under \"google.com\". That includes challenges for \"subdomain.google.com\". This MAY be Unicode or Punycode.", "location": "path", "required": true, "type": "string"}}, "path": "v1/acmeChallengeSets/{rootDomain}:rotateChallenges", "request": {"$ref": "RotateChallengesRequest"}, "response": {"$ref": "AcmeChallengeSet"}}}}}, "revision": "20240707", "rootUrl": "https://acmedns.googleapis.com/", "schemas": {"AcmeChallengeSet": {"description": "The up-to-date ACME challenge set on a domain for an RPC. This contains all of the ACME TXT records that exist on the domain.", "id": "AcmeChallengeSet", "properties": {"record": {"description": "The ACME challenges on the requested domain represented as individual TXT records.", "items": {"$ref": "AcmeTxtRecord"}, "type": "array"}}, "type": "object"}, "AcmeTxtRecord": {"description": "The TXT record message that represents an ACME DNS-01 challenge.", "id": "AcmeTxtRecord", "properties": {"digest": {"description": "Holds the ACME challenge data put in the TXT record. This will be checked to be a valid TXT record data entry.", "type": "string"}, "fqdn": {"description": "The domain/subdomain for the record. In a request, this MAY be Unicode or Punycode. In a response, this will be in Unicode. The fqdn MUST contain the root_domain field on the request.", "type": "string"}, "updateTime": {"description": "Output only. The time when this record was last updated. This will be in UTC time.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RotateChallengesRequest": {"description": "The request message for the RotateChallenges RPC. Requires an access token, a root domain, and either records_to_add or records_to_remove to be populated. Records may be set for multiple subdomains at once to support SAN requests for multiple subdomains in a single domain. By default, ACME TXT record challenges that are older than 30 days will be removed. Set `keep_expired_records` to false if this behavior is undesired. There is a record maximum of 100 records per domain including expired records. Any request sent that would exceed this maximum will result in a FAILED_PRECONDITION error. NEXT ID: 6", "id": "RotateChallengesRequest", "properties": {"accessToken": {"description": "Required. ACME DNS access token. This is a base64 token secret that is procured from the Google Domains website. It authorizes ACME TXT record updates for a domain.", "format": "byte", "type": "string"}, "keepExpiredRecords": {"description": "Keep records older than 30 days that were used for previous requests.", "type": "boolean"}, "recordsToAdd": {"description": "ACME TXT record challenges to add. Supports multiple challenges on the same FQDN.", "items": {"$ref": "AcmeTxtRecord"}, "type": "array"}, "recordsToRemove": {"description": "ACME TXT record challenges to remove.", "items": {"$ref": "AcmeTxtRecord"}, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "ACME DNS API", "version": "v1", "version_module": true}